{% extends "base.html" %}
{% block start %}
<form method="post" enctype="multipart/form-data">
  {% csrf_token %}
  <input type="text" name="title" placeholder="Enter title ...">
  <br><br>
  <input type="text" name="description" placeholder="Enter description ...">
  <br><br>
  <input type="file" name="image">
  <br><br>
  <button type="submit">Submit</button>
</form>

{% if create %}
  <h2>Saved Items:</h2>
  {% for item in create %}
    <div style="border: 1px solid #ccc; margin: 10px; padding: 10px;">
      <h3>{{ item.title }}</h3>
      <p>{{ item.description }}</p>
      {% if item.image %}
        <img src="{{ item.image.url }}" alt="{{ item.title }}" style="max-width: 200px;">
      {% endif %}
    </div>
  {% endfor %}
{% else %}
  <p>No items found.</p>
{% endif %}
{% endblock %}