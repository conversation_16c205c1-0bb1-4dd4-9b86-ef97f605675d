from django.shortcuts import render,redirect
from .models import *

# Create your views here.
def create(request):
  if request.method == "POST":
    data = request.POST
    title = data.get("title")
    description = data.get("description")
    image = request.FILES.get("image")

    Mainmodel.objects.create(
      title = title,
      description = description,
      image = image
    )

    return redirect("/")
  
  return render(request,"index.html")